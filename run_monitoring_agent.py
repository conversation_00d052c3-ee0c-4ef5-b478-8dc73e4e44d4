import asyncio
import logging
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent
from agents.system_status_monitor import SystemStatusMonitor

# Configure logging to DEBUG level for detailed output
logging.basicConfig(
    level=logging.INFO, # Keep root logger at INFO, specific agent will be DEBUG
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set the logger for the monitoring agent to DEBUG
monitoring_agent_logger = logging.getLogger('agents.options_market_monitoring_agent')
monitoring_agent_logger.setLevel(logging.DEBUG)

async def main():
    logger.info("[SCRIPT] Starting Options Market Monitoring Agent with System Status Monitor...")

    # Initialize both agents
    market_agent = OptionsMarketMonitoringAgent()
    status_monitor = SystemStatusMonitor()

    try:
        # Initialize system status monitor first
        status_success = await status_monitor.initialize(
            initial_balance=100000,
            daily_trades_target=50,
            trading_mode='paper'
        )

        if not status_success:
            logger.error("[SCRIPT] System Status Monitor initialization failed.")
            return

        # Initialize market monitoring agent
        market_success = await market_agent.initialize()
        if not market_success:
            logger.error("[SCRIPT] Market Monitoring Agent initialization failed.")
            return

        logger.info("[SCRIPT] Both agents initialized successfully. Starting continuous monitoring...")

        # Update initial status
        status_monitor.update_agent_status('market_monitoring', 'running', cycles_completed=1)

        # Create a wrapper task for the market agent that updates status
        async def market_agent_with_status():
            cycle_count = 1
            try:
                # Start the market agent in the background
                market_task = asyncio.create_task(market_agent.start())

                # Periodically update status while the agent runs
                while not market_task.done():
                    await asyncio.sleep(30)  # Update every 30 seconds
                    status_monitor.update_agent_status('market_monitoring', 'running', cycles_completed=cycle_count)
                    cycle_count += 1
                    logger.debug(f"[STATUS] Updated market monitoring agent status - Cycle {cycle_count}")

                # Wait for the market agent to complete
                return await market_task

            except Exception as e:
                logger.error(f"[ERROR] Market agent with status failed: {e}")
                status_monitor.update_agent_status('market_monitoring', 'error', errors_count=1)
                raise

        # Start both agents concurrently
        await asyncio.gather(
            status_monitor.start(),
            market_agent_with_status(),
            return_exceptions=True
        )

    except KeyboardInterrupt:
        logger.info("[SCRIPT] Agents interrupted by user")
    except Exception as e:
        logger.error(f"[SCRIPT] Error in main: {e}")
    finally:
        logger.info("[SCRIPT] Cleaning up agents...")
        await asyncio.gather(
            market_agent.cleanup(),
            status_monitor.cleanup(),
            return_exceptions=True
        )

if __name__ == "__main__":
    asyncio.run(main())
