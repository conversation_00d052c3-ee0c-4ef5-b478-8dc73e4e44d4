import asyncio
import logging
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent

# Configure logging to DEBUG level for detailed output
logging.basicConfig(
    level=logging.INFO, # Keep root logger at INFO, specific agent will be DEBUG
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set the logger for the monitoring agent to DEBUG
monitoring_agent_logger = logging.getLogger('agents.options_market_monitoring_agent')
monitoring_agent_logger.setLevel(logging.DEBUG)

async def main():
    logger.info("[SCRIPT] Starting Options Market Monitoring Agent for debugging...")
    agent = OptionsMarketMonitoringAgent()
    try:
        success = await agent.initialize()
        if success:
            # Run the agent for a short period to capture logs
            # In a real scenario, you might want to run it longer or until a specific event
            await asyncio.sleep(30) # Run for 30 seconds to capture some data processing
            logger.info("[SCRIPT] Agent run complete. Check logs for price change analysis.")
        else:
            logger.error("[SCRIPT] Agent initialization failed.")
    except KeyboardInterrupt:
        logger.info("[SCRIPT] Agent interrupted by user")
    except Exception as e:
        logger.error(f"[SCRIPT] Error in main: {e}")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
