#!/usr/bin/env python3
"""
API Rate Limiter for SmartAPI calls
Prevents API limit errors by adding delays between requests
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional
from functools import wraps

logger = logging.getLogger(__name__)

class APIRateLimiter:
    """Rate limiter for API calls to prevent hitting API limits"""
    
    def __init__(self, delay_seconds: float = 0.4):
        self.delay_seconds = delay_seconds
        self.last_call_time = 0.0
        
    async def wait_if_needed(self):
        """Wait if needed to respect rate limits"""
        current_time = time.time()
        time_since_last_call = current_time - self.last_call_time
        
        if time_since_last_call < self.delay_seconds:
            wait_time = self.delay_seconds - time_since_last_call
            logger.debug(f"[RATE_LIMIT] Waiting {wait_time:.2f}s to respect API limits")
            await asyncio.sleep(wait_time)
        
        self.last_call_time = time.time()

# Global rate limiter instance
api_rate_limiter = APIRateLimiter(delay_seconds=0.4)

def rate_limited(func):
    """Decorator to add rate limiting to API calls"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        await api_rate_limiter.wait_if_needed()
        return await func(*args, **kwargs)
    return wrapper

def rate_limited_sync(func):
    """Decorator to add rate limiting to synchronous API calls"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Convert to async and wait
        loop = asyncio.get_event_loop()
        loop.run_until_complete(api_rate_limiter.wait_if_needed())
        return func(*args, **kwargs)
    return wrapper