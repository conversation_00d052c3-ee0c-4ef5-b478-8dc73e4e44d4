# Options Performance Analysis Agent Configuration
performance_analysis:
  # Analysis intervals
  analysis_interval: 60  # seconds
  report_interval: 300  # seconds (5 minutes)
  
  # Metrics calculation
  calculate_sharpe: true
  calculate_sortino: true
  calculate_max_drawdown: true
  calculate_win_rate: true
  
  # Performance tracking
  track_daily_pnl: true
  track_trade_performance: true
  track_strategy_performance: true
  
  # Risk metrics
  risk_free_rate: 0.06  # 6% annual
  confidence_level: 0.95  # for VaR calculation
  
  # Reporting
  generate_reports: true
  report_format: "json"
  save_charts: false
  
  # Data retention
  keep_history_days: 90
  
  # Alert thresholds
  max_drawdown_alert: 0.10  # 10%
  daily_loss_alert: 0.05  # 5%
  
  # Output paths
  reports_path: "data/performance"
  charts_path: "data/charts"