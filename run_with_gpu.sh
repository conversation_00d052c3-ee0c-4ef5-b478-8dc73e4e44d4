#!/bin/bash

# GPU-Accelerated Options Trading System Runner
# This script sets up the proper environment for GPU acceleration

# Set CUDA library paths
export LD_LIBRARY_PATH="/usr/local/lib/ollama:/usr/local/cuda/lib64:/usr/lib/x86_64-linux-gnu:/opt/cuda/lib64:$LD_LIBRARY_PATH"

# Activate virtual environment
source .venv/bin/activate

# Check GPU availability
echo "🔍 Checking GPU availability..."
python -c "
import torch
print(f'PyTorch CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU: {torch.cuda.get_device_name(0)}')
    print(f'CUDA version: {torch.version.cuda}')
    print(f'GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB')

try:
    import cupy as cp
    print(f'CuPy available: True (version {cp.__version__})')
    # Test basic GPU operation
    arr = cp.array([1, 2, 3, 4, 5])
    result = cp.sum(arr)
    print(f'CuPy test successful: sum([1,2,3,4,5]) = {result}')
except Exception as e:
    print(f'CuPy available: False ({e})')

try:
    import cudf
    print(f'cuDF available: True (version {cudf.__version__})')
    # Test basic cuDF operation
    df = cudf.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})
    print(f'cuDF test successful: created DataFrame with {len(df)} rows')
except Exception as e:
    print(f'cuDF available: False ({e})')
"

echo ""
echo "📊 Initial GPU status:"
nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits

echo ""
echo "🚀 Starting Options Strategy Evolution with GPU acceleration..."
echo "Command: python main.py --agent strategy_evolution $@"
echo ""

# Start GPU monitoring in background
(
    echo "⏰ $(date): Starting GPU monitoring..."
    while true; do
        sleep 30
        echo "⏰ $(date): GPU Status - $(nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits | awk -F, '{printf "GPU: %s%%, Memory: %s/%s MB, Temp: %s°C", $1, $2, $3, $4}')"
    done
) &
GPU_MONITOR_PID=$!

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Stopping GPU monitoring..."
    kill $GPU_MONITOR_PID 2>/dev/null
    echo "📊 Final GPU status:"
    nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits
    exit
}

# Set trap to cleanup on script exit
trap cleanup EXIT INT TERM

# Run the main script with all passed arguments
python main.py --agent strategy_evolution "$@"
