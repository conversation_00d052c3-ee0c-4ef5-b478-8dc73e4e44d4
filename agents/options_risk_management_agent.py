#!/usr/bin/env python3
"""
Options Risk Management Agent - Comprehensive Options Risk Control

Features:
🛡️ 1. Greeks-Based Risk Management
- Delta exposure limits
- Gamma risk monitoring
- Theta decay management
- Vega volatility risk

📊 2. Portfolio Risk Controls
- Position size limits
- Correlation risk management
- Concentration limits
- Margin requirement monitoring

⚡ 3. Real-time Risk Monitoring
- Live P&L tracking
- Risk metric calculations
- Alert generation
- Automatic position adjustments

🎯 4. Advanced Risk Models
- VaR calculations
- Stress testing
- Scenario analysis
- Monte Carlo simulations
"""

import asyncio
import logging
import polars as pl
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import yaml # Added yaml import
import math # For potential calculations
from collections import deque # For win/loss streaks
# from .position_sizing_manager import PositionSizingManager, PositionSizeRequest
# Position sizing manager will be implemented separately

logger = logging.getLogger(__name__)

class OptionsRiskManagementAgent:
    """Options Risk Management Agent for comprehensive risk control"""
    
    def __init__(self, config_path: str = "config/options_risk_management_config.yaml"):
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.is_running = False
        self.total_capital: float = 100000.0 # Example total capital
        self.current_capital: float = self.total_capital
        self.daily_high_capital: float = self.total_capital
        self.daily_drawdown_loss: float = 0.0
        self.daily_drawdown_pct: float = 0.0
        self.trading_paused: bool = False
        self.pause_cool_off_until: Optional[datetime] = None
        self.active_trades: List[Dict[str, Any]] = [] # To track open trades
        self.trade_history: List[Dict[str, Any]] = [] # For logging and analysis
        self.consecutive_sl_hits: int = 0
        self.last_trade_time: Dict[str, datetime] = {} # For re-entry cool-off
        self.win_loss_streak: deque = deque(maxlen=5) # Track last 5 trade outcomes (True for win, False for loss)
        self.daily_capital_used: float = 0.0 # Track total capital used today
        self.daily_trades_count: int = 0 # Track number of trades today
        self.monitoring_tasks: List[asyncio.Task] = [] # Store background tasks

        # Position sizing manager
        # self.position_sizing_manager: Optional[PositionSizingManager] = None

        logger.info("🛡️ [INIT] Options Risk Management Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent"""
        try:
            await self._load_config()
            logger.info("✅ [SUCCESS] Options Risk Management Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from YAML file"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Config file not found at {self.config_path}")
        with open(self.config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        logger.info(f"⚙️ [CONFIG] Loaded configuration from {self.config_path}")
        # Ensure default values if not present in config
        self.total_capital = self.config.get('initial_capital', 100000.0)
        self.current_capital = self.total_capital
        self.daily_high_capital = self.total_capital
    
    async def start(self, **kwargs) -> bool:
        """Start the risk management agent"""
        try:
            logger.info("🚀 [START] Starting Options Risk Management Agent...")
            self.is_running = True

            # Reset daily metrics at start
            await self._reset_daily_metrics()

            # Extract trading mode and virtual account
            self.trading_mode = kwargs.get('trading_mode', 'real')
            self.virtual_account = kwargs.get('virtual_account')

            logger.info(f"🛡️ [MODE] Risk management mode: {self.trading_mode.upper()}")
            logger.info(f"🛡️ [STRATEGY] Options buying strategy - Entry/Exit/Hold decisions")

            # Initialize position sizing manager
            # self.position_sizing_manager = PositionSizingManager()
            # await self.position_sizing_manager.initialize(
            #     initial_balance=kwargs.get('initial_balance', 100000.0)
            # )

            # Start risk monitoring tasks with enhanced position management as background tasks
            self.monitoring_tasks = [
                asyncio.create_task(self._monitor_daily_drawdown()),
                asyncio.create_task(self._monitor_portfolio_exposure()),
                asyncio.create_task(self._monitor_greeks_exposure()),
                asyncio.create_task(self._generate_risk_alerts()),
                asyncio.create_task(self._broadcast_risk_summary()),
                asyncio.create_task(self._monitor_active_positions()),
                asyncio.create_task(self._make_entry_exit_hold_decisions()) # Moved to end for clarity
            ]

            logger.info("✅ [SUCCESS] Risk Management Agent started successfully")
            return True
        except Exception as e:
            logger.error(f"❌ [ERROR] Failed to start agent: {e}")
            return False

    async def cleanup(self):
        """Cleanup the agent and stop all background tasks"""
        try:
            logger.info("🛑 [CLEANUP] Stopping Risk Management Agent...")
            self.is_running = False

            # Cancel all monitoring tasks
            for task in self.monitoring_tasks:
                if not task.done():
                    task.cancel()

            # Wait for tasks to complete cancellation
            if self.monitoring_tasks:
                await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)

            logger.info("✅ [CLEANUP] Risk Management Agent stopped successfully")
        except Exception as e:
            logger.error(f"❌ [ERROR] Error during cleanup: {e}")
    
    async def _monitor_daily_drawdown(self):
        """Monitors daily drawdown and triggers protection"""
        while self.is_running:
            try:
                # Simulate updating current_capital and daily_high_capital
                # In a real system, this would come from an accounting or P&L module
                # For now, let's assume current_capital is updated externally
                
                # Update daily high
                self.daily_high_capital = max(self.daily_high_capital, self.current_capital)
                
                # Calculate absolute loss and percentage drawdown from daily high
                self.daily_drawdown_loss = self.daily_high_capital - self.current_capital
                if self.daily_high_capital > 0:
                    self.daily_drawdown_pct = self.daily_drawdown_loss / self.daily_high_capital
                else:
                    self.daily_drawdown_pct = 0.0

                max_drawdown_pct = self.config['risk_limits']['max_daily_drawdown_pct']
                max_loss_streak = self.config['risk_limits']['max_loss_streak']

                # Check for daily drawdown threshold
                if self.daily_drawdown_pct >= max_drawdown_pct and not self.trading_paused:
                    logger.warning(f"🚨 [DRAWDOWN] Daily drawdown {self.daily_drawdown_pct:.2%} reached! Pausing trading.")
                    self.trading_paused = True
                    cool_off_minutes = self.config['risk_limits']['drawdown_cool_off_minutes']
                    self.pause_cool_off_until = datetime.now() + timedelta(minutes=cool_off_minutes)
                    # TODO: Notify Performance Agent + Logging for AI Agent
                    await self._send_alert("Daily Drawdown Limit Reached", f"Trading paused due to {self.daily_drawdown_pct:.2%} drawdown.")
                elif self.trading_paused and datetime.now() >= self.pause_cool_off_until:
                    logger.info("✅ [DRAWDOWN] Cool-off window ended. Resuming trading.")
                    self.trading_paused = False
                    self.pause_cool_off_until = None
                
                # Check for win/loss streaks
                if len(self.win_loss_streak) == self.win_loss_streak.maxlen and all(not outcome for outcome in self.win_loss_streak):
                    if self.win_loss_streak.count(False) >= max_loss_streak and not self.trading_paused:
                        logger.warning(f"🚨 [STREAK] {max_loss_streak} consecutive losses detected! Pausing trading.")
                        self.trading_paused = True
                        cool_off_minutes = self.config['risk_limits']['loss_streak_cool_off_minutes']
                        self.pause_cool_off_until = datetime.now() + timedelta(minutes=cool_off_minutes)
                        await self._send_alert("Loss Streak Limit Reached", f"Trading paused due to {max_loss_streak} consecutive losses.")

                logger.info(f"📈 [DRAWDOWN] Current Drawdown: {self.daily_drawdown_pct:.2%} (Loss: ₹{self.daily_drawdown_loss:,.2f}). Win/Loss Streak: {list(self.win_loss_streak)}")
                await asyncio.sleep(self.config['monitoring_interval'])
            except Exception as e:
                logger.error(f"❌ [ERROR] Daily drawdown monitoring failed: {e}")

    async def _monitor_portfolio_exposure(self):
        """Monitors portfolio-level exposure controls"""
        while self.is_running:
            try:
                # Feature 7: Portfolio-Level Exposure Controls
                max_active_trades = self.config['risk_limits']['max_active_trades']
                max_sector_concentration_pct = self.config['risk_limits']['max_sector_concentration_pct']
                max_daily_capital_usage_pct = self.config['risk_limits']['max_daily_capital_usage_pct']
                max_strategy_allocation_pct = self.config['risk_limits']['max_strategy_allocation_pct']

                current_active_trades = len(self.active_trades)

                if current_active_trades > max_active_trades:
                    logger.warning(f"⚠️ [EXPOSURE] Exceeded max active trades: {current_active_trades}/{max_active_trades}")
                    # TODO: Implement logic to reduce exposure, e.g., close least profitable trade
                
                # Sector/asset concentration limits
                sector_exposure: Dict[str, float] = {}
                for trade in self.active_trades:
                    sector = trade.get('sector', 'UNKNOWN') # Assume trade has a 'sector' field
                    sector_exposure[sector] = sector_exposure.get(sector, 0.0) + trade.get('capital_at_risk', 0.0)
                
                for sector, exposure in sector_exposure.items():
                    if exposure / self.total_capital > max_sector_concentration_pct:
                        logger.warning(f"⚠️ [EXPOSURE] Sector '{sector}' concentration ({exposure/self.total_capital:.2%}) exceeds limit ({max_sector_concentration_pct:.2%}).")
                        # TODO: Implement logic to reduce sector exposure

                # Total capital used today/this week
                if self.daily_capital_used / self.total_capital > max_daily_capital_usage_pct:
                    logger.warning(f"⚠️ [EXPOSURE] Daily capital usage ({self.daily_capital_used/self.total_capital:.2%}) exceeds limit ({max_daily_capital_usage_pct:.2%}).")
                    # TODO: Consider pausing new trades for the day

                # Strategy-level exposure
                strategy_exposure: Dict[str, float] = {}
                for trade in self.active_trades:
                    strategy_id = trade.get('strategy_id', 'UNKNOWN') # Assume trade has 'strategy_id'
                    strategy_exposure[strategy_id] = strategy_exposure.get(strategy_id, 0.0) + trade.get('capital_at_risk', 0.0)
                
                for strategy, exposure in strategy_exposure.items():
                    if exposure / self.total_capital > max_strategy_allocation_pct:
                        logger.warning(f"⚠️ [EXPOSURE] Strategy '{strategy}' allocation ({exposure/self.total_capital:.2%}) exceeds limit ({max_strategy_allocation_pct:.2%}).")
                        # TODO: Implement logic to reduce allocation for this strategy or block new signals from it

                logger.info(f"📊 [EXPOSURE] Active Trades: {current_active_trades}/{max_active_trades}. Daily Capital Used: ₹{self.daily_capital_used:,.2f}")
                await asyncio.sleep(self.config['monitoring_interval'])
            except Exception as e:
                logger.error(f"❌ [ERROR] Portfolio exposure monitoring failed: {e}")
    
    async def _monitor_greeks_exposure(self):
        """Monitor Greeks exposure"""
        while self.is_running:
            try:
                logger.info("📐 [RISK] Monitoring Greeks exposure...")
                # TODO: Implement actual Greeks calculation and limits check
                await asyncio.sleep(self.config['monitoring_interval'])
            except Exception as e:
                logger.error(f"❌ [ERROR] Greeks monitoring failed: {e}")
    
    async def _generate_risk_alerts(self):
        """Generate risk alerts"""
        while self.is_running:
            try:
                logger.info("🔔 [ALERT] Checking for risk alerts...")
                # This method will be called by other methods when an alert condition is met
                await asyncio.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"❌ [ERROR] Risk alert generation failed: {e}")

    async def _broadcast_risk_summary(self):
        """Broadcasts real-time risk summary"""
        while self.is_running:
            try:
                summary = await self.get_real_time_risk_summary()
                logger.info(f"📢 [SUMMARY] Real-time Risk Summary: {json.dumps(summary)}")
                # TODO: Send this summary to a message queue or UI dashboard
                await asyncio.sleep(self.config['monitoring_interval'] * 2) # Broadcast less frequently
            except Exception as e:
                logger.error(f"❌ [ERROR] Risk summary broadcast failed: {e}")

    async def get_real_time_risk_summary(self) -> Dict[str, Any]:
        """Feature 11: Outputs a real-time risk summary"""
        # Placeholder values for now
        available_capital = self.current_capital
        active_trades_count = len(self.active_trades)
        current_drawdown_pct = self.daily_drawdown_pct
        max_risk_per_trade = self.config['risk_limits']['max_capital_at_risk_pct'] * 100 # as percentage
        capital_at_risk = sum(trade.get('capital_at_risk', 0) for trade in self.active_trades) # Sum of capital at risk from active trades
        risk_mode = await self._get_current_risk_mode()
        next_allowed_signal_time = "N/A"
        if self.trading_paused and self.pause_cool_off_until:
            next_allowed_signal_time = self.pause_cool_off_until.strftime("%H:%M")

        return {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M"),
            "available_capital": round(available_capital, 2),
            "active_trades": active_trades_count,
            "current_drawdown_pct": round(current_drawdown_pct * 100, 2),
            "max_risk_per_trade_pct": round(max_risk_per_trade, 2),
            "capital_at_risk": round(capital_at_risk, 2),
            "risk_mode": risk_mode,
            "next_allowed_signal_time": next_allowed_signal_time
        }

    async def _get_current_risk_mode(self) -> str:
        """Determines the current risk mode based on market conditions and internal state."""
        # This is a simplified example. In a real system, this would involve
        # querying market monitoring agent for regime, volatility, etc.
        if self.trading_paused:
            return "halted"
        if self.daily_drawdown_pct >= self.config['risk_limits']['max_daily_drawdown_pct'] * 0.8: # 80% of max drawdown
            return "high_alert"
        if len(self.active_trades) >= self.config['risk_limits']['max_active_trades']:
            return "max_exposure"
        
        # Example: Check for high IV regime from config
        # For a real implementation, this would come from a market monitoring agent
        current_market_regime = "normal" # Placeholder
        current_volatility_regime = "normal" # Placeholder
        current_event_regime = "normal" # Placeholder

        for rule in self.config['regime_modulation']['rules']:
            if (rule['market_regime'] == current_market_regime or rule['market_regime'] == "any") and \
               (rule['volatility_regime'] == current_volatility_regime or rule['volatility_regime'] == "any") and \
               (rule['event_regime'] == current_event_regime or rule['event_regime'] == "any"):
                if rule['max_capital_at_risk_pct'] <= 0.005: # Example threshold for low risk
                    return "conservative"
                elif rule['max_capital_at_risk_pct'] >= 0.015: # Example threshold for high risk
                    return "aggressive"
        
        return "moderate"

    async def _send_alert(self, title: str, message: str):
        """Sends an alert to an external system (e.g., Telegram, UI)"""
        logger.warning(f"🔔 ALERT: {title} - {message}")
        # TODO: Integrate with an actual alert system (e.g., Execution Agent, LLM Interface Agent)

    async def evaluate_signal_for_risk(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Feature 1: Capital-at-Risk Enforcement
        Feature 4: Signal Quality Filters
        Feature 5: Trade Conflict & Overlap Resolution
        Feature 8: Risk Score Calculation Per Trade
        Feature 10: Logging, Audit, and Feedback
        Feature 12: LLM-Readable Explanation Output
        """
        decision = {
            "approved": True,
            "downscaled": False,
            "reason": [],
            "risk_score": 1.0, # Default to 1.0 (no issues)
            "adjusted_lot_size": signal.get('lot_size', 1),
            "capital_at_risk": 0.0 # Calculated later
        }
        explanation_messages = []
        
        # 0. Check if trading is paused
        if self.trading_paused:
            decision['approved'] = False
            decision['reason'].append("Trading paused due to daily drawdown protection.")
            explanation_messages.append("Trade blocked: System is currently paused due to daily drawdown limits.")
            logger.warning(f"🚫 [BLOCKED] Signal {signal.get('signal_id')} blocked: Trading paused.")
            await self._log_trade_decision(signal, decision)
            return decision

        # 1. Capital-at-Risk Enforcement (pre-calculation)
        # This needs total capital, lot size, option premium, confidence score
        # For now, let's assume signal has 'estimated_premium', 'lot_size', 'entry_price', 'stop_loss'
        estimated_premium = signal.get('estimated_premium', 0)
        lot_size = signal.get('lot_size', 1)
        entry_price = signal.get('entry_price', 0)
        stop_loss = signal.get('stop_loss', 0)
        
        # Apply regime-based modulation to get max_capital_at_risk_pct and max_lot_size
        current_regime_params = await self._get_regime_modulated_params(signal)
        max_capital_at_risk_pct = current_regime_params['max_capital_at_risk_pct']
        max_allowed_lot_size = current_regime_params['max_lot_size']

        # Calculate capital at risk for this specific trade based on SL distance
        trade_capital_at_risk = 0.0
        if entry_price > 0 and stop_loss > 0:
            # Assuming risk is the difference between entry and stop loss, multiplied by lot size
            trade_capital_at_risk = abs(entry_price - stop_loss) * lot_size
        else:
            # Fallback to premium-based if SL not available, or use a default risk per lot
            trade_capital_at_risk = estimated_premium * lot_size * signal.get('risk_per_lot_factor', 0.5) # Use a default factor if SL not defined
            explanation_messages.append("Warning: Stop loss not defined, using estimated premium for capital at risk calculation.")

        decision['capital_at_risk'] = trade_capital_at_risk

        # Check against total capital at risk limit
        total_capital_at_risk_after_this_trade = sum(t.get('capital_at_risk', 0) for t in self.active_trades) + trade_capital_at_risk
        max_total_capital_at_risk = self.total_capital * max_capital_at_risk_pct

        if total_capital_at_risk_after_this_trade > max_total_capital_at_risk:
            decision['approved'] = False
            decision['downscaled'] = True
            decision['reason'].append(f"Capital at risk ({total_capital_at_risk_after_this_trade:,.2f}) exceeds limit ({max_total_capital_at_risk:,.2f}).")
            explanation_messages.append(f"Trade downscaled: Capital at risk would exceed {max_capital_at_risk_pct:.2%} of total capital.")
            
            # Downscale logic: Calculate new lot size based on remaining risk capacity
            remaining_risk_capacity = max_total_capital_at_risk - sum(t.get('capital_at_risk', 0) for t in self.active_trades)
            
            if trade_capital_at_risk > 0 and lot_size > 0: # Ensure no division by zero
                risk_per_lot = trade_capital_at_risk / lot_size
                if risk_per_lot > 0:
                    adjusted_lot_size = int(remaining_risk_capacity / risk_per_lot)
                    decision['adjusted_lot_size'] = max(0, adjusted_lot_size) # Ensure non-negative
                    if decision['adjusted_lot_size'] == 0:
                        decision['approved'] = False # Block if cannot take any lot
                        explanation_messages[-1] = f"Trade blocked: Capital at risk would exceed {max_capital_at_risk_pct:.2%} of total capital, no remaining capacity for this trade."
                    else:
                        explanation_messages[-1] = f"Trade downscaled: Capital at risk would exceed {max_capital_at_risk_pct:.2%} of total capital. Adjusted lot size to {decision['adjusted_lot_size']}."
                else:
                    decision['approved'] = False # Cannot calculate, so block
                    explanation_messages.append("Trade blocked: Cannot calculate adjusted lot size due to zero risk per lot.")
            else:
                decision['approved'] = False # Cannot calculate, so block
                explanation_messages.append("Trade blocked: Cannot calculate adjusted lot size due to invalid trade parameters (premium/risk factor/lot size).")

        # Apply max_allowed_lot_size from regime modulation
        if decision['adjusted_lot_size'] > max_allowed_lot_size:
            original_adjusted_lot_size = decision['adjusted_lot_size']
            decision['adjusted_lot_size'] = max_allowed_lot_size
            decision['downscaled'] = True
            decision['reason'].append(f"Lot size ({original_adjusted_lot_size}) exceeds regime-based max ({max_allowed_lot_size}).")
            explanation_messages.append(f"Trade downscaled: Lot size adjusted to {max_allowed_lot_size} due to current market regime.")
            if decision['adjusted_lot_size'] == 0:
                decision['approved'] = False
                explanation_messages[-1] = f"Trade blocked: Lot size adjusted to 0 due to current market regime."

        # 4. Signal Quality Filters
        min_confidence = self.config['risk_limits']['min_signal_confidence']
        if signal.get('confidence_score', 0) < min_confidence:
            decision['approved'] = False
            decision['reason'].append(f"Signal confidence ({signal.get('confidence_score', 0):.2f}) below threshold ({min_confidence}).")
            explanation_messages.append(f"Trade blocked: Signal confidence was only {signal.get('confidence_score', 0):.2f}.")
            decision['risk_score'] -= 0.2 # Reduce risk score

        # Placeholder: Strategy has <50% recent win rate (via Performance Agent)
        # In a real system, this would query the Performance Agent
        strategy_id = signal.get('strategy_id', 'UNKNOWN_STRATEGY')
        # For now, simulate a low win rate for a specific strategy
        if strategy_id == "strat_014" and self.config['risk_limits'].get('simulate_low_win_rate', False):
            decision['approved'] = False
            decision['reason'].append(f"Strategy '{strategy_id}' has low recent win rate (<50%).")
            explanation_messages.append(f"Trade blocked: Strategy {strategy_id} has a low recent win rate.")
            decision['risk_score'] -= 0.2

        # Placeholder: Strategy was disabled by Strategy Evolution Agent
        # In a real system, this would query the Strategy Evolution Agent
        if signal.get('strategy_disabled', False): # Assume signal carries this flag
            decision['approved'] = False
            decision['reason'].append(f"Strategy '{strategy_id}' was disabled by Evolution Agent.")
            explanation_messages.append(f"Trade blocked: Strategy {strategy_id} was disabled by the Evolution Agent.")
            decision['risk_score'] -= 0.3

        # Signal conflicts with previous trade (overlapping or overexposed) - partially covered by capital at risk
        # Additional check: if the signal is for an instrument already held in the same direction
        for active_trade in self.active_trades:
            if active_trade.get('symbol') == signal.get('symbol') and \
               active_trade.get('expiry') == signal.get('expiry') and \
               active_trade.get('strike') == signal.get('strike') and \
               active_trade.get('option_type') == signal.get('option_type') and \
               active_trade.get('trade_type') == signal.get('trade_type'):
                decision['approved'] = False
                decision['reason'].append("Signal conflicts with an existing open trade (same instrument, same direction).")
                explanation_messages.append("Trade blocked: Conflicting signal with an existing open trade.")
                decision['risk_score'] -= 0.2
                break


        # 5. Trade Conflict & Overlap Resolution
        # Prevent simultaneous CE + PE buy in same expiry/strike
        if signal.get('trade_type') == 'BUY' and signal.get('option_type') in ['CE', 'PE']:
            for active_trade in self.active_trades:
                if active_trade.get('symbol') == signal.get('symbol') and \
                   active_trade.get('expiry') == signal.get('expiry') and \
                   active_trade.get('strike') == signal.get('strike') and \
                   active_trade.get('trade_type') == 'BUY' and \
                   active_trade.get('option_type') != signal.get('option_type'):
                    decision['approved'] = False
                    decision['reason'].append("Simultaneous CE + PE buy for same expiry/strike detected.")
                    explanation_messages.append("Trade blocked: Simultaneous CE and PE buy for the same strike and expiry is not allowed.")
                    decision['risk_score'] -= 0.3
                    break
        
        # Same-strike re-entry within X minutes of SL
        re_entry_cool_off_minutes = self.config['risk_limits']['re_entry_cool_off_minutes']
        trade_identifier = f"{signal.get('symbol')}_{signal.get('strike')}_{signal.get('option_type')}"
        if trade_identifier in self.last_trade_time:
            time_since_last_trade = (datetime.now() - self.last_trade_time[trade_identifier]).total_seconds() / 60
            if time_since_last_trade < re_entry_cool_off_minutes:
                decision['approved'] = False
                decision['reason'].append(f"Same-strike re-entry within {re_entry_cool_off_minutes} minutes detected.")
                explanation_messages.append(f"Trade blocked: Re-entry for {trade_identifier} attempted within {re_entry_cool_off_minutes} minutes of last trade.")
                decision['risk_score'] -= 0.2

        # Overlap of 2+ signals in same regime (unless ensemble strategy explicitly allows)
        # This requires tracking signals within the current regime. For simplicity, let's check for too many active trades in the current regime.
        current_regime = await self._get_current_risk_mode() # Re-use risk mode as regime indicator
        trades_in_current_regime = [t for t in self.active_trades if t.get('risk_mode_at_entry') == current_regime]
        max_signals_in_regime = self.config['risk_limits'].get('max_signals_in_regime', 3) # Example limit
        
        if len(trades_in_current_regime) >= max_signals_in_regime and not signal.get('ensemble_allowed', False):
            decision['approved'] = False
            decision['reason'].append(f"Too many active signals ({len(trades_in_current_regime)}) in current '{current_regime}' regime.")
            explanation_messages.append(f"Trade blocked: Overlap of signals in '{current_regime}' regime, and not an ensemble strategy.")
            decision['risk_score'] -= 0.1

        # 8. Risk Score Calculation Per Trade (adjust based on checks)
        # Initial risk score is 1.0. Deduct based on issues.
        # Incorporate drawdown risk, SL distance, volatility, strategy past ROI
        
        # Drawdown risk: Higher if current drawdown is high
        if self.daily_drawdown_pct > 0.02: # Example threshold for higher drawdown risk
            decision['risk_score'] -= 0.1 * (self.daily_drawdown_pct / self.config['risk_limits']['max_daily_drawdown_pct'])

        # SL distance: Closer SL means lower risk (but higher chance of hit)
        # Assuming signal provides 'entry_price' and 'stop_loss'
        entry_price = signal.get('entry_price', 0)
        stop_loss = signal.get('stop_loss', 0)
        if entry_price > 0 and stop_loss > 0:
            sl_distance_pct = abs(entry_price - stop_loss) / entry_price
            if sl_distance_pct < self.config['risk_limits'].get('min_sl_distance_pct', 0.005): # Too tight SL
                decision['risk_score'] -= 0.05
                explanation_messages.append("Warning: Stop loss is very tight, increasing risk of premature exit.")
            elif sl_distance_pct > self.config['risk_limits'].get('max_sl_distance_pct', 0.05): # Too wide SL
                decision['risk_score'] -= 0.1
                explanation_messages.append("Warning: Stop loss is too wide, increasing potential loss per trade.")

        # Volatility: Higher volatility means higher risk
        # Assuming signal provides 'current_iv' or 'vix'
        current_iv = signal.get('current_iv', 0)
        if current_iv > self.config['risk_limits'].get('high_iv_threshold', 20.0):
            decision['risk_score'] -= 0.1
            explanation_messages.append("Warning: High implied volatility detected, increasing trade risk.")

        # Strategy past ROI (placeholder - would come from Performance Agent)
        strategy_past_roi = signal.get('strategy_past_roi', 0.0) # Example: 0.05 for 5% ROI
        if strategy_past_roi < self.config['risk_limits'].get('min_strategy_roi', 0.0):
            decision['risk_score'] -= 0.1
            explanation_messages.append("Warning: Strategy has a low past ROI, indicating potential underperformance.")

        # Ensure risk score is within [0, 1]
        decision['risk_score'] = max(0.0, min(1.0, decision['risk_score']))

        if not decision['approved']:
            decision['risk_score'] = max(0.0, decision['risk_score'] - 0.5) # Significant reduction if blocked

        # 12. LLM-Readable Explanation Output
        decision['llm_explanation'] = ". ".join(explanation_messages) if explanation_messages else "Trade approved: No significant risk concerns detected."

        # 10. Logging, Audit, and Feedback
        await self._log_trade_decision(signal, decision)

        # Save risk override for signal generation agent if needed
        if not decision['approved'] or decision['adjusted_lot_size'] != signal.get('lot_size', 1):
            await self._save_risk_override(signal, decision)

        return decision

    async def _save_risk_override(self, signal: Dict[str, Any], risk_decision: Dict[str, Any]):
        """Save risk management override for signal generation agent"""
        try:
            overrides_path = Path("data/risk_management")
            overrides_path.mkdir(parents=True, exist_ok=True)
            overrides_file = overrides_path / "signal_overrides.json"

            # Load existing overrides
            existing_overrides = []
            if overrides_file.exists():
                try:
                    with open(overrides_file, 'r') as f:
                        data = json.load(f)
                        existing_overrides = data.get('overrides', [])
                except:
                    pass

            # Create new override
            override = {
                'timestamp': datetime.now().isoformat(),
                'signal_id': signal.get('signal_id', 'unknown'),
                'underlying': signal.get('underlying', 'unknown'),
                'strategy_id': signal.get('strategy_id', 'unknown'),
                'type': 'block_trades' if not risk_decision['approved'] else 'adjust_position_size',
                'reason': risk_decision.get('reason', 'Risk management override'),
                'original_lot_size': signal.get('lot_size', 1),
                'adjusted_lot_size': risk_decision.get('adjusted_lot_size', 0),
                'risk_score': risk_decision.get('risk_score', 0)
            }

            existing_overrides.append(override)

            # Keep only recent overrides (last 100)
            if len(existing_overrides) > 100:
                existing_overrides = existing_overrides[-100:]

            # Save overrides
            with open(overrides_file, 'w') as f:
                json.dump({'overrides': existing_overrides}, f, indent=2)

            logger.info(f"[OVERRIDE] Saved risk override for {signal.get('underlying', 'unknown')} signal")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save risk override: {e}")

    async def _get_regime_modulated_params(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Feature 3: Regime-Based Risk Modulation
        Dynamically adjusts max capital risk or exposure based on market regime.
        """
        # These would typically come from a Market Monitoring Agent
        current_market_regime = signal.get('market_regime', 'normal') # e.g., "trending", "choppy", "volatile"
        current_volatility_regime = signal.get('volatility_regime', 'normal') # e.g., "low_iv", "moderate_iv", "high_iv"
        current_event_regime = signal.get('event_regime', 'normal') # e.g., "normal", "rbi_policy", "budget_day", "expiry"

        default_params = {
            "max_lot_size": 1, # Default conservative
            "max_capital_at_risk_pct": self.config['risk_limits']['max_capital_at_risk_pct']
        }

        for rule in self.config['regime_modulation']['rules']:
            market_match = (rule['market_regime'] == "any" or rule['market_regime'] == current_market_regime)
            volatility_match = (rule['volatility_regime'] == "any" or rule['volatility_regime'] == current_volatility_regime)
            event_match = (rule['event_regime'] == "any" or rule['event_regime'] == current_event_regime)

            if market_match and volatility_match and event_match:
                logger.info(f"⚙️ [REGIME] Applying regime rule: {rule}")
                return {
                    "max_lot_size": rule.get('max_lot_size', default_params['max_lot_size']),
                    "max_capital_at_risk_pct": rule.get('max_capital_at_risk_pct', default_params['max_capital_at_risk_pct'])
                }
        
        logger.warning(f"⚠️ [REGIME] No specific regime rule matched for market={current_market_regime}, vol={current_volatility_regime}, event={current_event_regime}. Using default parameters.")
        return default_params

    async def adjust_stop_loss_target(self, trade: Dict[str, Any]) -> Dict[str, Any]:
        """
        Feature 6: Dynamic Stop Loss / Target Adjustment
        Modifies SL and TP based on real-time ATR / volatility spike, execution slippage, trade confidence.
        """
        adjusted_trade = trade.copy()
        
        # Placeholder for real-time ATR/Volatility
        current_atr = trade.get('current_atr', 10.0) # Example ATR
        vix_spike = trade.get('vix_spike', 0.0) # Example VIX spike (e.g., 0.0 for no spike, 0.2 for 20% spike)
        execution_slippage = trade.get('execution_slippage', 0.0) # Example slippage in points
        trade_confidence = trade.get('confidence_score', 0.7) # From signal

        original_sl = adjusted_trade.get('stop_loss', 0.0)
        original_tp = adjusted_trade.get('target_price', 0.0)
        
        # Adjust SL based on VIX spike / volatility
        sl_buffer_multiplier = 1.0
        if vix_spike > 0.15: # If VIX spiked by more than 15%
            sl_buffer_multiplier = 1.25 # Increase SL buffer
            logger.info(f"📈 [SL/TP] Increasing SL buffer to {sl_buffer_multiplier}xATR due to VIX spike.")
        
        # Example: SL is ATR-based
        if original_sl == 0.0: # If SL not set, set it based on ATR
            adjusted_trade['stop_loss'] = adjusted_trade['entry_price'] - (current_atr * sl_buffer_multiplier)
        else: # Adjust existing SL
            adjusted_trade['stop_loss'] = original_sl - (current_atr * (sl_buffer_multiplier - 1.0)) # Adjust by difference

        # Adjust TP based on confidence and slippage
        if trade_confidence < 0.6:
            adjusted_trade['target_price'] = original_tp * 0.9 # Reduce target if confidence is low
            logger.info(f"📉 [SL/TP] Reducing target price due to low trade confidence ({trade_confidence}).")
        
        adjusted_trade['stop_loss'] -= execution_slippage # Account for slippage
        adjusted_trade['target_price'] += execution_slippage # Account for slippage

        logger.info(f"🔄 [SL/TP] Adjusted SL from {original_sl:.2f} to {adjusted_trade['stop_loss']:.2f}, TP from {original_tp:.2f} to {adjusted_trade['target_price']:.2f}")
        return adjusted_trade

    async def _log_trade_decision(self, signal: Dict[str, Any], decision: Dict[str, Any]):
        """
        Feature 10: Logging, Audit, and Feedback
        Logs each blocked/downscaled/approved trade with reason and risk parameters.
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "signal_id": signal.get('signal_id'),
            "symbol": signal.get('symbol'),
            "decision": "APPROVED" if decision['approved'] and not decision['downscaled'] else ("DOWNSCALED" if decision['downscaled'] else "BLOCKED"),
            "reason": decision['reason'],
            "risk_score": decision['risk_score'],
            "risk_parameters_at_decision": {
                "max_capital_at_risk_pct": self.config['risk_limits']['max_capital_at_risk_pct'],
                "max_daily_drawdown_pct": self.daily_drawdown_pct,
                "current_active_trades": len(self.active_trades),
                "current_capital": self.current_capital,
                "daily_high_capital": self.daily_high_capital,
                "trading_paused": self.trading_paused,
                "consecutive_sl_hits": self.consecutive_sl_hits,
                "llm_explanation": decision.get('llm_explanation', '')
            },
            "signal_details": {
                "confidence_score": signal.get('confidence_score'),
                "lot_size": signal.get('lot_size'),
                "estimated_premium": signal.get('estimated_premium'),
                "trade_type": signal.get('trade_type'),
                "option_type": signal.get('option_type'),
                "strike": signal.get('strike'),
                "expiry": signal.get('expiry'),
                "market_regime": signal.get('market_regime'),
                "volatility_regime": signal.get('volatility_regime'),
                "event_regime": signal.get('event_regime')
            },
            "adjusted_lot_size": decision.get('adjusted_lot_size', signal.get('lot_size'))
        }
        self.trade_history.append(log_entry)
        logger.info(f"📝 [LOG] Trade decision logged: {log_entry['decision']} for signal {log_entry['signal_id']}. Reason: {', '.join(log_entry['reason'])}")
        
        # TODO: Send logs to AI Training Agent, Performance Agent, Strategy Evolution Agent

    async def emergency_safeguard_trigger(self, reason: str):
        """
        Feature 9: Emergency Safeguards (Hard Stops)
        Triggers a trading halt and sends shutdown commands.
        """
        logger.critical(f"🚨🚨🚨 [EMERGENCY] Activating emergency safeguard: {reason}! Halting all trading operations.")
        self.is_running = False # Stop this agent's loops
        self.trading_paused = True # Ensure trading remains paused

        # Send shutdown commands to other agents
        # TODO: Integrate with actual agent communication mechanism
        logger.critical("🛑 [SHUTDOWN] Sending shutdown command to Execution Agent.")
        # await self.execution_agent.shutdown() # Example
        logger.critical("🛑 [SHUTDOWN] Sending shutdown command to Strategy Generator.")
        # await self.strategy_generator_agent.stop_generation() # Example
        
        await self._send_alert("EMERGENCY HALT", f"System-wide trading halt triggered: {reason}")
        
        # Log the emergency event
        self.trade_history.append({
            "timestamp": datetime.now().isoformat(),
            "event_type": "EMERGENCY_HALT",
            "reason": reason,
            "system_state": {
                "current_capital": self.current_capital,
                "active_trades": len(self.active_trades),
                "daily_drawdown_pct": self.daily_drawdown_pct,
                "consecutive_sl_hits": self.consecutive_sl_hits
            }
        })

    async def update_trade_status(self, trade_update: Dict[str, Any]):
        """
        Updates the status of an active trade (e.g., filled, closed, SL hit, TP hit).
        This method would be called by the Execution Agent.
        """
        trade_id = trade_update.get('trade_id')
        status = trade_update.get('status')
        pnl = trade_update.get('pnl', 0.0)
        
        found = False
        for i, trade in enumerate(self.active_trades):
            if trade.get('trade_id') == trade_id:
                if status == 'CLOSED':
                    self.active_trades.pop(i)
                    self.current_capital += pnl # Update capital based on PnL
                    logger.info(f"✅ [TRADE] Trade {trade_id} closed. PnL: {pnl:,.2f}. Current Capital: {self.current_capital:,.2f}")
                    
                    # Update win/loss streak (Feature 2)
                    if pnl > 0:
                        self.win_loss_streak.append(True) # Win
                    else:
                        self.win_loss_streak.append(False) # Loss

                    # Check for consecutive SL hits (Feature 9)
                    if trade_update.get('exit_reason') == 'STOP_LOSS':
                        self.consecutive_sl_hits += 1
                        logger.warning(f"🔥 [SL HIT] Consecutive SL hits: {self.consecutive_sl_hits}")
                        if self.consecutive_sl_hits >= self.config['risk_limits']['max_consecutive_sl_hits']:
                            await self.emergency_safeguard_trigger(f"{self.consecutive_sl_hits} consecutive stop loss hits.")
                    else:
                        self.consecutive_sl_hits = 0 # Reset if not SL hit
                    
                    # Update last trade time for re-entry cool-off
                    trade_identifier = f"{trade.get('symbol')}_{trade.get('strike')}_{trade.get('option_type')}"
                    self.last_trade_time[trade_identifier] = datetime.now()

                else:
                    self.active_trades[i].update(trade_update) # Update other trade details
                found = True
                break
        
        if not found:
            logger.warning(f"⚠️ [TRADE] Received update for unknown trade ID: {trade_id}")

    async def _monitor_active_positions(self):
        """Monitors active positions for real-time adjustments or alerts."""
        while self.is_running:
            try:
                # This method would periodically check active_trades and
                # potentially interact with other agents (e.g., Execution Agent)
                # For now, it's a placeholder for future enhancements.
                if self.active_trades:
                    logger.info(f"📊 [POSITIONS] Monitoring {len(self.active_trades)} active trades.")
                    # Example: Check for trades nearing stop loss or target
                    for trade in self.active_trades:
                        # Placeholder for real-time price fetching
                        current_price = trade.get('current_price', trade.get('entry_price')) # Assume current_price is updated externally
                        if current_price <= trade.get('stop_loss', 0):
                            logger.warning(f"🚨 [POSITION ALERT] Trade {trade.get('trade_id')} nearing Stop Loss!")
                            # TODO: Send alert to Execution Agent to prepare for exit
                        elif current_price >= trade.get('target_price', float('inf')):
                            logger.info(f"🎯 [POSITION ALERT] Trade {trade.get('trade_id')} nearing Target Price!")
                            # TODO: Send alert to Execution Agent to prepare for exit
                else:
                    logger.info("📊 [POSITIONS] No active trades to monitor.")
                await asyncio.sleep(self.config['monitoring_interval'])
            except Exception as e:
                logger.error(f"❌ [ERROR] Active positions monitoring failed: {e}")

    async def _make_entry_exit_hold_decisions(self):
        """
        Feature 13: Entry/Exit/Hold Decision Logic for Options Buying Strategy
        This method will continuously evaluate market conditions and active trades
        to make decisions for options buying strategies.
        """
        while self.is_running:
            try:
                logger.info("🧠 [DECISION] Making entry/exit/hold decisions...")
                # This is a placeholder. In a real system, this would involve:
                # 1. Receiving real-time market data and signals.
                # 2. Evaluating active trades for exit conditions (SL/TP, time decay, volatility changes).
                # 3. Evaluating new signals for entry conditions.
                # 4. Interacting with the Execution Agent to place/modify/cancel orders.

                # Example: Check if any active trade needs to be exited
                for trade in list(self.active_trades): # Iterate over a copy to allow modification
                    # Placeholder for exit logic
                    # if trade_meets_exit_criteria(trade):
                    #     logger.info(f"🚪 [EXIT] Trade {trade.get('trade_id')} meets exit criteria. Requesting exit.")
                    #     # await self.execution_agent.request_exit(trade) # Example interaction
                    pass

                # Example: Check for new entry signals (would come from Signal Generation Agent)
                # new_signal = await self.signal_generation_agent.get_new_signal()
                # if new_signal:
                #     risk_decision = await self.evaluate_signal_for_risk(new_signal)
                #     if risk_decision['approved'] and risk_decision['adjusted_lot_size'] > 0:
                #         logger.info(f"✅ [ENTRY] Signal {new_signal.get('signal_id')} approved for entry. Requesting execution.")
                #         # await self.execution_agent.request_entry(new_signal, risk_decision['adjusted_lot_size'])

                await asyncio.sleep(self.config['monitoring_interval'])
            except Exception as e:
                logger.error(f"❌ [ERROR] Entry/Exit/Hold decision making failed: {e}")

    async def _reset_daily_metrics(self):
        """Resets daily metrics at the start of a new trading day."""
        # This method would typically be called by a scheduler at market open
        # For demonstration, we can call it periodically or based on a time check
        now = datetime.now()
        # Check if it's a new day (e.g., after market open)
        # For simplicity, let's assume a daily reset at a specific time or on first run of the day
        # A more robust solution would involve checking the date and ensuring it only runs once per day
        if not hasattr(self, '_last_daily_reset_date') or self._last_daily_reset_date != now.date():
            logger.info("☀️ [DAILY RESET] Resetting daily risk metrics.")
            self.daily_high_capital = self.current_capital
            self.daily_drawdown_loss = 0.0
            self.daily_drawdown_pct = 0.0
            self.daily_capital_used = 0.0
            self.daily_trades_count = 0
            self.win_loss_streak.clear()
            self.consecutive_sl_hits = 0
            self.last_trade_time.clear() # Clear re-entry cool-off times
            self.trading_paused = False
            self.pause_cool_off_until = None
            self._last_daily_reset_date = now.date() # Mark the date of the last reset

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("🧹 [CLEANUP] Cleaning up Options Risk Management Agent...")
            self.is_running = False
            logger.info("✅ [SUCCESS] Options Risk Management Agent cleaned up")
        except Exception as e:
            logger.error(f"❌ [ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    agent = OptionsRiskManagementAgent()
    try:
        await agent.initialize()
        
        # Simulate some initial capital and daily high
        agent.total_capital = 100000.0
        agent.current_capital = 100000.0
        agent.daily_high_capital = 100000.0

        # Simulate a signal for evaluation
        sample_signal = {
            "signal_id": "SIG_001",
            "symbol": "BANKNIFTY",
            "expiry": "2025-07-25",
            "strike": 45000,
            "option_type": "CE",
            "trade_type": "BUY",
            "lot_size": 2,
            "estimated_premium": 200.0,
            "confidence_score": 0.75,
            "risk_per_lot_factor": 1.0,
            "market_regime": "trending",
            "volatility_regime": "low_iv",
            "event_regime": "normal",
            "entry_price": 200.0, # For SL/TP adjustment
            "stop_loss": 150.0,
            "target_price": 250.0,
            "current_atr": 50.0,
            "vix_spike": 0.0,
            "execution_slippage": 0.0
        }

        # Evaluate the signal
        risk_decision = await agent.evaluate_signal_for_risk(sample_signal)
        logger.info(f"Decision for SIG_001: {risk_decision}")

        if risk_decision['approved'] and risk_decision['adjusted_lot_size'] > 0:
            # Simulate adding trade to active trades if approved
            approved_trade = sample_signal.copy()
            approved_trade['lot_size'] = risk_decision['adjusted_lot_size']
            approved_trade['capital_at_risk'] = risk_decision['capital_at_risk'] # Use calculated capital at risk
            approved_trade['trade_id'] = "TRADE_001"
            
            # Adjust SL/TP dynamically
            adjusted_trade = await agent.adjust_stop_loss_target(approved_trade)
            agent.active_trades.append(adjusted_trade)
            logger.info(f"Trade {adjusted_trade['trade_id']} added to active trades with adjusted SL/TP: {adjusted_trade['stop_loss']:.2f}/{adjusted_trade['target_price']:.2f}")

        # Simulate another signal that might be blocked
        blocked_signal = {
            "signal_id": "SIG_002",
            "symbol": "BANKNIFTY",
            "expiry": "2025-07-25",
            "strike": 45000,
            "option_type": "PE", # Conflict with SIG_001 if both are BUY
            "trade_type": "BUY",
            "lot_size": 2,
            "estimated_premium": 180.0,
            "confidence_score": 0.55, # Low confidence
            "risk_per_lot_factor": 1.0,
            "market_regime": "volatile", # High risk regime
            "volatility_regime": "high_iv",
            "event_regime": "normal",
            "entry_price": 180.0,
            "stop_loss": 130.0,
            "target_price": 230.0,
            "current_atr": 60.0,
            "vix_spike": 0.2, # VIX spike
            "execution_slippage": 2.0
        }
        risk_decision_blocked = await agent.evaluate_signal_for_risk(blocked_signal)
        logger.info(f"Decision for SIG_002: {risk_decision_blocked}")

        # Simulate a daily drawdown event
        agent.current_capital = 92000.0 # Simulate a loss
        await agent._monitor_daily_drawdown() # Manually trigger for demo

        # Simulate a trade closing with SL hit
        await agent.update_trade_status({
            "trade_id": "TRADE_001",
            "status": "CLOSED",
            "exit_reason": "STOP_LOSS",
            "pnl": -1000.0 # Simulate loss
        })
        
        # Simulate consecutive SL hits to trigger emergency safeguard
        agent.consecutive_sl_hits = 3 # Already 3 hits
        await agent.update_trade_status({
            "trade_id": "TRADE_002", # Dummy trade
            "status": "CLOSED",
            "exit_reason": "STOP_LOSS",
            "pnl": -500.0
        })

        await agent.start() # Start the continuous monitoring loops
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()



if __name__ == "__main__":
    asyncio.run(main())
