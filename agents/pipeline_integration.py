#!/usr/bin/env python3
"""
Pipeline Integration for Options Market Monitoring Agent

This module provides integration methods for the live trading pipeline
to work with the market monitoring agent.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from agents.options_market_monitoring_agent import OptionsMarketMonitoringAgent

logger = logging.getLogger(__name__)

class PipelineIntegration:
    """Integration layer between live trading pipeline and market monitoring agent"""
    
    def __init__(self):
        self.monitoring_agent: Optional[OptionsMarketMonitoringAgent] = None
        self.is_initialized = False
    
    async def initialize_monitoring_agent(self) -> bool:
        """Initialize the market monitoring agent"""
        try:
            logger.info("[PIPELINE] Initializing market monitoring agent...")
            
            self.monitoring_agent = OptionsMarketMonitoringAgent()
            success = await self.monitoring_agent.initialize()
            
            if success:
                # Start the monitoring agent in background
                asyncio.create_task(self.monitoring_agent.start())
                self.is_initialized = True
                logger.info("[PIPELINE] Market monitoring agent initialized and started")
                return True
            else:
                logger.error("[PIPELINE] Failed to initialize market monitoring agent")
                return False
                
        except Exception as e:
            logger.error(f"[PIPELINE] Error initializing monitoring agent: {e}")
            return False
    
    def process_tick_data(self, tick_data: Dict[str, Any]):
        """Process tick data through the monitoring agent"""
        try:
            if self.is_initialized and self.monitoring_agent:
                self.monitoring_agent.process_tick_data(tick_data)
        except Exception as e:
            logger.error(f"[PIPELINE] Error processing tick data: {e}")
    
    def process_candle_data(self, candle_data: Dict[str, Any]):
        """Process completed candle data"""
        try:
            if self.is_initialized and self.monitoring_agent:
                # The monitoring agent will read candle data from files
                # This is just for logging
                underlying = candle_data.get('underlying')
                timeframe = candle_data.get('timeframe')
                logger.debug(f"[PIPELINE] Candle completed: {underlying} {timeframe}")
        except Exception as e:
            logger.error(f"[PIPELINE] Error processing candle data: {e}")
    
    async def cleanup(self):
        """Cleanup the monitoring agent"""
        try:
            if self.monitoring_agent:
                await self.monitoring_agent.cleanup()
                logger.info("[PIPELINE] Market monitoring agent cleaned up")
        except Exception as e:
            logger.error(f"[PIPELINE] Error cleaning up monitoring agent: {e}")

# Global instance for the pipeline to use
pipeline_integration = PipelineIntegration()

# Convenience functions for the main pipeline
async def initialize_market_monitoring() -> bool:
    """Initialize market monitoring for the pipeline"""
    return await pipeline_integration.initialize_monitoring_agent()

def process_pipeline_tick(tick_data: Dict[str, Any]):
    """Process tick data from the pipeline"""
    pipeline_integration.process_tick_data(tick_data)

def process_pipeline_candle(candle_data: Dict[str, Any]):
    """Process candle data from the pipeline"""
    pipeline_integration.process_candle_data(candle_data)

async def cleanup_market_monitoring():
    """Cleanup market monitoring"""
    await pipeline_integration.cleanup()