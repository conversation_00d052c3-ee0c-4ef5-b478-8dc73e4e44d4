#!/usr/bin/env python3
"""
Options Market Monitoring Agent - Real-time Multi-Timeframe Market Surveillance

Integrates with live trading pipeline:
- Reads from data/live/{1min,3min,5min,15min} directories
- Uses historical data from data/historical for indicator initialization
- Processes real-time tick data for technical analysis
"""

import asyncio
import logging
import polars as pl
from datetime import datetime, time, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import yaml
import aiofiles
import polars_talib as pt

# Technical indicators integration
from agents.polars_technical_indicators_manager import PolarsTechnicalIndicatorsManager, MarketRegime
# API rate limiting
from utils.api_rate_limiter import api_rate_limiter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OptionsMarketMonitoringAgent:
    """Options Market Monitoring Agent for real-time market surveillance"""
    
    def __init__(self, config_path: str = "config/options_market_monitoring_config.yaml"):
        self.config_path = Path(config_path)
        self.config = None
        self.is_running = False

        # Data paths for multi-timeframe monitoring
        self.data_path = Path("data")
        self.live_path = self.data_path / "live"
        self.timeframes = ["1min", "3min", "5min", "15min"]

        # Market data cache for each timeframe
        self.market_data_cache = {tf: {} for tf in self.timeframes}
        self.last_update = {tf: {} for tf in self.timeframes}
        self.market_regime_state = {tf: {} for tf in self.timeframes}
        self.alerts_cache = []
        self.anomaly_logs = []
        self.strategy_suppression_decisions = []

        # Technical indicators manager
        self.indicators_manager = PolarsTechnicalIndicatorsManager()

        # Real-time OHLC aggregation for technical indicators
        self.tick_aggregator = {}
        self.last_bar_time = {}

        logger.info("[INIT] Options Market Monitoring Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent"""
        try:
            await self._load_config()

            # Initialize paths
            self.live_path = Path("data/live")
            self.alerts_path = Path("data/alerts")
            self.alerts_path.mkdir(parents=True, exist_ok=True)

            # Clean up old data files to prevent stale data issues
            await self._cleanup_old_data()

            # Initialize data structures
            self.market_data_cache = {timeframe: {} for timeframe in self.config['timeframes']}
            self.last_update = {timeframe: None for timeframe in self.config['timeframes']}
            self.alerts_cache = []

            await self._initialize_technical_indicators()
            logger.info("[SUCCESS] Options Market Monitoring Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from YAML file"""
        try:
            async with aiofiles.open(self.config_path, mode="r") as f:
                content = await f.read()
                self.config = yaml.safe_load(content)
            logger.info(f"[CONFIG] Configuration loaded from {self.config_path}")
        except FileNotFoundError:
            logger.error(f"[ERROR] Config file not found at {self.config_path}")
            raise
        except Exception as e:
            logger.error(f"[ERROR] Error loading config: {e}")
            raise

    async def _cleanup_old_data(self):
        """Clean up old data files to prevent stale data issues"""
        try:
            import os

            # Define cutoff time (data older than 1 hour will be removed)
            cutoff_time = datetime.now() - timedelta(hours=1)
            cutoff_timestamp = cutoff_time.timestamp()

            cleaned_count = 0

            # Clean up live data directories
            if self.live_path.exists():
                for timeframe in self.config['timeframes']:
                    timeframe_path = self.live_path / timeframe
                    if timeframe_path.exists():
                        for file_path in timeframe_path.glob("*.parquet"):
                            try:
                                # Check file modification time
                                file_mtime = file_path.stat().st_mtime
                                if file_mtime < cutoff_timestamp:
                                    file_path.unlink()
                                    cleaned_count += 1
                                    logger.debug(f"[CLEANUP] Removed old data file: {file_path}")
                            except Exception as e:
                                logger.warning(f"[CLEANUP] Failed to remove {file_path}: {e}")

            if cleaned_count > 0:
                logger.info(f"[CLEANUP] Removed {cleaned_count} old data files")
            else:
                logger.debug("[CLEANUP] No old data files to clean up")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup old data: {e}")

    async def _initialize_technical_indicators(self):
        """Initialize technical indicators with historical data from pipeline"""
        try:
            logger.info("[INDICATORS] Loading historical data for technical indicators...")

            for underlying in self.config['underlying_symbols']:
                historical_data = await self._load_historical_data_for_indicators(underlying)

                if historical_data is not None and historical_data.height >= 50:
                    for row in historical_data.iter_rows(named=True):
                        ohlc_data = {
                            'timestamp': row['timestamp'],
                            'open': row['open'],
                            'high': row['high'],
                            'low': row['low'],
                            'close': row['close'],
                            'volume': row.get('volume', 0)
                        }
                        await self.indicators_manager.update_indicators(underlying, ohlc_data)

                    logger.info(f"[INDICATORS] Loaded {historical_data.height} historical bars for {underlying}")
                else:
                    logger.warning(f"[INDICATORS] No historical data found for {underlying} - will build indicators from live data")

            logger.info("[SUCCESS] Technical indicators initialized")

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize technical indicators: {e}")

    async def _load_historical_data_for_indicators(self, underlying: str) -> Optional[pl.DataFrame]:
        """Load historical OHLC data from pipeline's premarket data"""
        try:
            # Add rate limiting for file operations
            await api_rate_limiter.wait_if_needed()
            
            # Load from historical data directory (8 days premarket data)
            historical_dir = Path("data/historical")
            
            # Try different timeframes
            timeframes = ["5min", "1min"]
            
            for timeframe in timeframes:
                data_dir = historical_dir / timeframe
                if not data_dir.exists():
                    continue
                    
                pattern = f"{underlying}_{timeframe}_*.parquet"
                files = list(data_dir.glob(pattern))
                
                if files:
                    latest_file = max(files, key=lambda x: x.stat().st_mtime)
                    df = pl.read_parquet(latest_file)
                    
                    if df.height >= 50:
                        result_df = df.tail(200)
                        logger.info(f"[INDICATORS] Loaded {result_df.height} bars from {latest_file.name} for {underlying}")
                        return result_df
            
            # Fallback: try live data
            live_dir = Path("data/live/1min")
            if live_dir.exists():
                pattern = f"{underlying}_1min_*.parquet"
                files = list(live_dir.glob(pattern))
                if files:
                    latest_file = max(files, key=lambda x: x.stat().st_mtime)
                    df = pl.read_parquet(latest_file)
                    if df.height >= 50:
                        result_df = df.tail(200)
                        logger.info(f"[INDICATORS] Loaded {result_df.height} bars from live data for {underlying}")
                        return result_df

            logger.warning(f"[INDICATORS] No sufficient historical data found for {underlying}")
            return None

        except Exception as e:
            logger.error(f"[ERROR] Failed to load historical data for {underlying}: {e}")
            return None
    
    async def start(self, **kwargs) -> bool:
        """Start the multi-timeframe market monitoring agent"""
        try:
            logger.info("[START] Starting Options Market Monitoring Agent...")
            self.is_running = True

            # Start monitoring tasks for each timeframe
            monitoring_tasks = []

            for timeframe in self.timeframes:
                monitoring_tasks.extend([
                    self._monitor_timeframe_data(timeframe),
                    self._detect_timeframe_regime(timeframe),
                ])

            # Add general monitoring tasks
            monitoring_tasks.extend([
                self._generate_multi_timeframe_alerts(),
                self._detect_anomalies(),
                self._generate_summary_output(),
            ])

            await asyncio.gather(*monitoring_tasks)
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def _monitor_timeframe_data(self, timeframe: str):
        """Monitor data for specific timeframe"""
        while self.is_running:
            try:
                interval = self.config['monitoring_intervals'][timeframe]

                # Load latest data from pipeline
                await self._load_latest_timeframe_data(timeframe)

                # Analyze data
                await self._analyze_timeframe_data(timeframe)

                logger.debug(f"[MONITOR] Monitoring {timeframe} data...")
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"[ERROR] {timeframe} data monitoring failed: {e}")

    async def _load_latest_timeframe_data(self, timeframe: str):
        """Load latest data from pipeline's timeframe directories"""
        try:
            # Add rate limiting for file operations
            await api_rate_limiter.wait_if_needed()

            timeframe_path = self.live_path / timeframe
            current_market_data = {}

            # Check if live data directory exists
            if not timeframe_path.exists():
                logger.warning(f"[LOAD] Live data directory does not exist: {timeframe_path}")
                logger.info(f"[LOAD] This is normal if data ingestion pipeline hasn't started yet")
                self.market_data_cache[timeframe] = current_market_data
                self.last_update[timeframe] = datetime.now()
                return

            files_found = 0
            for underlying in self.config['underlying_symbols']:
                # Load index OHLC data from pipeline
                pattern = f"{underlying}_{timeframe}_*.parquet"
                files = list(timeframe_path.glob(pattern))

                if files:
                    latest_file = max(files, key=lambda x: x.stat().st_mtime)

                    # Check if file is recent (within last 2 hours)
                    file_age = datetime.now() - datetime.fromtimestamp(latest_file.stat().st_mtime)

                    if file_age > timedelta(hours=2):
                        logger.warning(f"[LOAD] Data file is old ({file_age}): {latest_file}")
                        continue

                    try:
                        data = pl.read_parquet(latest_file)

                        if data.height > 0:
                            current_market_data[underlying] = data
                            files_found += 1
                            logger.debug(f"[LOAD] Loaded {underlying} {timeframe}: {data.height} records from {latest_file.name}")
                        else:
                            logger.warning(f"[LOAD] Empty data file: {latest_file}")
                    except Exception as e:
                        logger.error(f"[LOAD] Failed to read {latest_file}: {e}")
                else:
                    logger.debug(f"[LOAD] No {timeframe} data files found for {underlying} in {timeframe_path}")

            if files_found == 0:
                logger.warning(f"[LOAD] No valid data files found for {timeframe} timeframe")
                logger.info(f"[LOAD] This may indicate data ingestion pipeline is not running or no recent data available")
            else:
                logger.debug(f"[LOAD] Successfully loaded {files_found} data files for {timeframe}")

            self.market_data_cache[timeframe] = current_market_data
            self.last_update[timeframe] = datetime.now()

        except Exception as e:
            logger.error(f"[ERROR] Failed to load {timeframe} data: {e}")
            # Initialize empty cache to prevent crashes
            self.market_data_cache[timeframe] = {}
            self.last_update[timeframe] = datetime.now()

    async def _analyze_timeframe_data(self, timeframe: str):
        """Analyze data for specific timeframe"""
        try:
            # Check if we have any data for this timeframe
            if timeframe not in self.market_data_cache or not self.market_data_cache[timeframe]:
                logger.debug(f"[ANALYZE] No market data cache available for {timeframe}")
                return

            data_count = 0
            for underlying in self.config['underlying_symbols']:
                if underlying in self.market_data_cache[timeframe]:
                    data = self.market_data_cache[timeframe][underlying]

                    if data is not None and data.height > 0:
                        data_count += 1
                        await self._analyze_price_movements(data, underlying, timeframe)
                        await self._analyze_volume_patterns(data, underlying, timeframe)
                        await self._analyze_volatility_changes(data, underlying, timeframe)
                        await self._detect_anomalies_for_timeframe(data, underlying, timeframe)
                    else:
                        logger.debug(f"[ANALYZE] No data available for {underlying} {timeframe}")
                else:
                    logger.debug(f"[ANALYZE] {underlying} not found in {timeframe} cache")

            if data_count == 0:
                logger.info(f"[ANALYZE] No valid data found for analysis in {timeframe} timeframe")

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze {timeframe} data: {e}")

    async def _analyze_price_movements(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Analyze price movements for alerts"""
        try:
            if data.height < 2:
                logger.debug(f"[ANALYZE] Not enough data for price movement analysis in {underlying} {timeframe}. Data height: {data.height}")
                return

            latest_prices = data.tail(2)
            if latest_prices.height >= 2:
                current_price = latest_prices['close'].to_list()[-1]
                previous_price = latest_prices['close'].to_list()[-2]

                price_change = (current_price - previous_price) / previous_price

                threshold = self.config['alert_thresholds']['price_change']
                
                logger.debug(f"[PRICE_ANALYZE] {underlying} {timeframe}: Current Price={current_price:.2f}, Previous Price={previous_price:.2f}, Price Change={price_change:.2%}, Threshold={threshold:.2%}")
                
                if abs(price_change) > threshold:
                    alert_msg = f"🚨 [ALERT] {underlying} {timeframe}: Significant Price Change {price_change:.2%}"
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "price_change", "underlying": underlying, "timeframe": timeframe, "value": price_change, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze price movements for {underlying} {timeframe}: {e}")
            logger.debug(f"[ERROR_DATA] Data causing error for {underlying} {timeframe}:\n{data}")

    async def _analyze_volume_patterns(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Analyze volume patterns for unusual activity"""
        try:
            if data.height < 10:
                return

            recent_data = data.tail(10)
            avg_volume = recent_data['volume'].mean()
            latest_volume = recent_data['volume'].to_list()[-1]

            volume_ratio = latest_volume / avg_volume if avg_volume > 0 else 0
            threshold = self.config['alert_thresholds']['volume_spike']

            if volume_ratio > threshold:
                alert_msg = f"📈 [ALERT] {underlying} {timeframe}: Volume Spike {volume_ratio:.2f}x"
                logger.warning(alert_msg)
                self.alerts_cache.append({"type": "volume_spike", "underlying": underlying, "timeframe": timeframe, "value": volume_ratio, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze volume patterns: {e}")

    async def _analyze_volatility_changes(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Analyze volatility changes"""
        try:
            if data.height < 20:
                return

            recent_data = data.tail(20)
            returns = recent_data.with_columns([
                (pl.col('close').pct_change()).alias('returns')
            ])['returns'].drop_nulls()

            if returns.len() > 0:
                volatility = returns.std()

                threshold = self.config['alert_thresholds']['volatility_spike']
                if volatility > threshold:
                    alert_msg = f"⚡ [ALERT] {underlying} {timeframe}: Volatility Spike {volatility:.4f}"
                    logger.warning(alert_msg)
                    self.alerts_cache.append({"type": "volatility_spike", "underlying": underlying, "timeframe": timeframe, "value": volatility, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze volatility changes: {e}")

    async def _detect_timeframe_regime(self, timeframe: str):
        """Detect market regime for specific timeframe"""
        while self.is_running:
            try:
                interval = self.config['monitoring_intervals'][timeframe]
                await self._analyze_market_regime(timeframe)
                logger.debug(f"[REGIME] Detecting {timeframe} market regime...")
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"[ERROR] {timeframe} regime detection failed: {e}")

    async def _analyze_market_regime(self, timeframe: str):
        """Analyze market regime for specific timeframe"""
        try:
            regime_output = {}
            for underlying in self.config['underlying_symbols']:
                if underlying in self.market_data_cache[timeframe]:
                    data = self.market_data_cache[timeframe][underlying]
                    if data.height < self.config['market_regime_params']['trend_lookback']:
                        continue

                    # Trend detection using SMA
                    data_with_sma = data.with_columns(
                        pt.sma(pl.col("close"), timeperiod=self.config['market_regime_params']['trend_lookback']).alias("sma")
                    )
                    current_close = data_with_sma['close'].to_list()[-1]
                    sma_value = data_with_sma['sma'].to_list()[-1]

                    trend = "sideways"
                    if current_close > sma_value * 1.005:
                        trend = "bullish"
                    elif current_close < sma_value * 0.995:
                        trend = "bearish"

                    # Momentum using RSI
                    data_with_rsi = data.with_columns(
                        pt.rsi(pl.col("close"), timeperiod=14).alias("rsi")
                    )
                    current_rsi = data_with_rsi['rsi'].to_list()[-1]
                    momentum_state = "neutral_momentum"
                    if current_rsi > self.config['market_regime_params']['momentum_rsi_threshold']:
                        momentum_state = "exhaustion"
                    elif current_rsi < 30:
                        momentum_state = "acceleration"

                    regime_output[underlying] = {
                        "trend": trend,
                        "momentum_state": momentum_state,
                    }
            
            self.market_regime_state[timeframe] = regime_output

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze {timeframe} market regime: {e}")

    async def _generate_multi_timeframe_alerts(self):
        """Generate alerts based on multi-timeframe analysis"""
        while self.is_running:
            try:
                await self._pre_validate_signals_and_alert()
                logger.debug("[ALERT] Checking multi-timeframe alerts...")
                await asyncio.sleep(60)

            except Exception as e:
                logger.error(f"[ERROR] Multi-timeframe alert generation failed: {e}")

    async def _pre_validate_signals_and_alert(self):
        """Pre-validate signals and generate risk alerts"""
        try:
            current_regime = self.market_regime_state.get('15min', {}).get('NIFTY', {})
            
            if current_regime:
                market_regime_type = current_regime.get('trend', 'unknown')

                if market_regime_type == "sideways" and "breakout" in [a['type'] for a in self.alerts_cache if 'type' in a]:
                    alert_msg = "⚠️ [PRE-VALIDATION] Market is sideways, breakout strategy might be incompatible."
                    logger.warning(alert_msg)
                    self.strategy_suppression_decisions.append({"reason": "sideways_breakout_incompatible", "timestamp": datetime.now()})

            # Risk signal
            overall_risk_signal = "risk-on"
            if len(self.strategy_suppression_decisions) > 0 or len([a for a in self.alerts_cache if a['type'] == 'anomaly']) > 0:
                overall_risk_signal = "risk-off"
            
            if overall_risk_signal == "risk-off":
                alert_msg = "🛑 [RISK SIGNAL] Sending 'risk-off' signal for capital modulation."
                logger.warning(alert_msg)
            else:
                logger.info("✅ [RISK SIGNAL] Market conditions are 'risk-on'.")

        except Exception as e:
            logger.error(f"[ERROR] Signal pre-validation failed: {e}")

    async def _detect_anomalies(self):
        """Detect market anomalies"""
        while self.is_running:
            try:
                logger.debug("[ANOMALY] Checking for anomalies...")
                await asyncio.sleep(self.config['monitoring_intervals']['1min'])

            except Exception as e:
                logger.error(f"[ERROR] Anomaly detection failed: {e}")

    async def _detect_anomalies_for_timeframe(self, data: pl.DataFrame, underlying: str, timeframe: str):
        """Detect anomalies within specific timeframe data"""
        try:
            # Volume spike without price move
            if 'volume' in data.columns and 'close' in data.columns and data.height > 2:
                current_volume = data['volume'].to_list()[-1]
                previous_volume = data['volume'].to_list()[-2]
                current_price = data['close'].to_list()[-1]
                previous_price = data['close'].to_list()[-2]

                volume_change = (current_volume - previous_volume) / previous_volume if previous_volume != 0 else 0
                price_change = (current_price - previous_price) / previous_price if previous_price != 0 else 0

                if abs(volume_change) > 2.0 and abs(price_change) < 0.01:
                    anomaly_msg = f"⚠️ [ANOMALY] {underlying} {timeframe}: Volume spike without price move"
                    logger.warning(anomaly_msg)
                    self.anomaly_logs.append({"type": "volume_price_divergence", "underlying": underlying, "timeframe": timeframe, "timestamp": datetime.now()})
                    self.alerts_cache.append({"type": "anomaly", "message": anomaly_msg, "timestamp": datetime.now()})

        except Exception as e:
            logger.error(f"[ERROR] Anomaly detection for {timeframe} failed: {e}")

    async def _generate_summary_output(self):
        """Generate real-time market summary"""
        while self.is_running:
            try:
                # Add rate limiting for summary generation
                await api_rate_limiter.wait_if_needed()
                
                summary = {
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "market_regime": "unknown",
                    "volatility_regime": "unknown",
                    "recommended_assets": [],
                    "strategy_restrictions": [],
                    "alerts": []
                }

                # Get market conditions from technical indicators
                main_underlying = self.config['underlying_symbols'][0] if self.config['underlying_symbols'] else 'NIFTY'
                market_conditions = self.indicators_manager.get_market_conditions(main_underlying)

                if market_conditions:
                    summary["market_regime"] = f"{market_conditions.regime.value}_{market_conditions.momentum_state}"
                    summary["volatility_regime"] = "high_vol" if market_conditions.volatility_level > 0.25 else "low_vol"
                    summary["trend_strength"] = market_conditions.trend_strength
                    summary["support_level"] = market_conditions.support_level
                    summary["resistance_level"] = market_conditions.resistance_level
                    logger.debug(f"[INDICATORS] Using real market conditions for {main_underlying}")
                else:
                    logger.error(f"[ERROR] No technical indicators data available for {main_underlying}. Market analysis will be limited.")
                    summary["market_regime"] = "unknown_insufficient_data"
                    summary["volatility_regime"] = "unknown_insufficient_data"

                # Add recommended assets
                summary["recommended_assets"] = self.config['underlying_symbols']

                # Add alerts
                for alert in self.alerts_cache:
                    if alert['type'] == 'price_change':
                        summary["alerts"].append(f"Significant price change for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'volume_spike':
                        summary["alerts"].append(f"Volume spike for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'volatility_spike':
                        summary["alerts"].append(f"Volatility spike for {alert['underlying']} {alert['timeframe']}")
                    elif alert['type'] == 'anomaly':
                        summary["alerts"].append(alert['message'])

                # Clear alerts cache
                self.alerts_cache.clear()

                # Save summary
                output_path = Path(self.config['output_settings']['summary_output_path'])
                output_path.parent.mkdir(parents=True, exist_ok=True)
                async with aiofiles.open(output_path, mode="w") as f:
                    await f.write(json.dumps(summary, indent=2))
                logger.info(f"📊 [OUTPUT] Market summary saved to {output_path}")

                await asyncio.sleep(60)

            except Exception as e:
                logger.error(f"[ERROR] Summary output generation failed: {e}")

    def process_tick_data(self, tick_data: Dict[str, Any]):
        """Process tick data from live trading pipeline"""
        try:
            # Extract underlying from tick data
            symbol = tick_data.get('symbol', '')
            underlying = None

            if 'NIFTY' in symbol and 'BANK' not in symbol:
                underlying = 'NIFTY'
            elif 'BANKNIFTY' in symbol or 'BANK NIFTY' in symbol:
                underlying = 'BANKNIFTY'

            if not underlying:
                return

            ltp = float(tick_data.get('ltp', 0))
            volume = float(tick_data.get('volume', 0))
            timestamp = datetime.now()

            # Aggregate ticks into 1-minute OHLC bars
            current_minute = timestamp.replace(second=0, microsecond=0)

            if underlying not in self.tick_aggregator:
                self.tick_aggregator[underlying] = {
                    'timestamp': current_minute,
                    'open': ltp,
                    'high': ltp,
                    'low': ltp,
                    'close': ltp,
                    'volume': volume
                }
                self.last_bar_time[underlying] = current_minute
            else:
                if current_minute > self.last_bar_time[underlying]:
                    # Complete previous bar
                    completed_bar = self.tick_aggregator[underlying].copy()
                    asyncio.create_task(self.indicators_manager.update_indicators(underlying, completed_bar))

                    # Start new bar
                    self.tick_aggregator[underlying] = {
                        'timestamp': current_minute,
                        'open': ltp,
                        'high': ltp,
                        'low': ltp,
                        'close': ltp,
                        'volume': volume
                    }
                    self.last_bar_time[underlying] = current_minute

                    logger.debug(f"[CANDLE] {underlying} 1min: O={completed_bar['open']:.2f} H={completed_bar['high']:.2f} L={completed_bar['low']:.2f} C={completed_bar['close']:.2f}")
                else:
                    # Update current bar
                    bar = self.tick_aggregator[underlying]
                    bar['high'] = max(bar['high'], ltp)
                    bar['low'] = min(bar['low'], ltp)
                    bar['close'] = ltp
                    bar['volume'] += volume

        except Exception as e:
            logger.error(f"[ERROR] Failed to process tick data: {e}")

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Market Monitoring Agent...")
            self.is_running = False
            logger.info("[SUCCESS] Options Market Monitoring Agent cleaned up")
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    agent = OptionsMarketMonitoringAgent()
    try:
        success = await agent.initialize()
        if success:
            await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    except Exception as e:
        logger.error(f"Error in main: {e}")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
